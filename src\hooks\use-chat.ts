import { useState, useEffect, useCallback } from 'react';
import { useSocket } from '@/components/providers/socket-provider';
import { useSession } from 'next-auth/react';

interface Message {
  id: string;
  body?: string;
  image?: string;
  createdAt: string;
  senderId: string;
  conversationId: string;
  sender: {
    id: string;
    name: string;
    image?: string;
  };
  seenBy: Array<{
    user: {
      id: string;
      name: string;
    };
  }>;
}

interface Conversation {
  id: string;
  name?: string;
  isGroup: boolean;
  participants: Array<{
    user: {
      id: string;
      name: string;
      email: string;
      image?: string;
    };
  }>;
  messages: Message[];
}

export function useChat() {
  const { socket } = useSocket();
  const { data: session } = useSession();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);

  // Fetch conversations
  const fetchConversations = useCallback(async () => {
    try {
      const response = await fetch('/api/conversations');
      const data = await response.json();
      setConversations(data.conversations || []);
    } catch (error) {
      console.error('Error fetching conversations:', error);
    }
  }, []);

  // Fetch messages for a conversation
  const fetchMessages = useCallback(async (conversationId: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/conversations/${conversationId}/messages`);
      const data = await response.json();
      setMessages(data.messages || []);
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Send a message
  const sendMessage = useCallback(async (conversationId: string, body: string) => {
    if (!body.trim() || !session?.user?.id) return;

    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ body }),
      });

      const data = await response.json();
      
      if (response.ok && socket) {
        // Emit to Socket.IO for real-time updates
        socket.emit('send-message', {
          ...data.message,
          conversationId,
        });
        
        // Add message to local state
        setMessages(prev => [...prev, data.message]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }, [socket, session?.user?.id]);

  // Create or get conversation
  const createConversation = useCallback(async (participantIds: string[], isGroup = false, name?: string) => {
    try {
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ participantIds, isGroup, name }),
      });

      const data = await response.json();
      
      if (response.ok) {
        await fetchConversations(); // Refresh conversations list
        return data.conversation;
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
    }
  }, [fetchConversations]);

  // Join conversation
  const joinConversation = useCallback((conversationId: string) => {
    if (socket && conversationId !== currentConversation) {
      // Leave previous conversation
      if (currentConversation) {
        socket.emit('leave-conversation', currentConversation);
      }
      
      // Join new conversation
      socket.emit('join-conversation', conversationId);
      setCurrentConversation(conversationId);
      fetchMessages(conversationId);
    }
  }, [socket, currentConversation, fetchMessages]);

  // Typing indicators
  const startTyping = useCallback((conversationId: string) => {
    if (socket && session?.user) {
      socket.emit('typing-start', {
        conversationId,
        userId: session.user.id,
        userName: session.user.name,
      });
    }
  }, [socket, session?.user]);

  const stopTyping = useCallback((conversationId: string) => {
    if (socket && session?.user) {
      socket.emit('typing-stop', {
        conversationId,
        userId: session.user.id,
      });
    }
  }, [socket, session?.user]);

  // Socket event listeners
  useEffect(() => {
    if (!socket) return;

    const handleNewMessage = (message: Message) => {
      setMessages(prev => [...prev, message]);
    };

    const handleUserTyping = ({ userId, userName }: { userId: string; userName: string }) => {
      setTypingUsers(prev => new Set(prev).add(userId));
    };

    const handleUserStopTyping = ({ userId }: { userId: string }) => {
      setTypingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    };

    socket.on('new-message', handleNewMessage);
    socket.on('user-typing', handleUserTyping);
    socket.on('user-stop-typing', handleUserStopTyping);

    return () => {
      socket.off('new-message', handleNewMessage);
      socket.off('user-typing', handleUserTyping);
      socket.off('user-stop-typing', handleUserStopTyping);
    };
  }, [socket]);

  // Initial data fetch
  useEffect(() => {
    if (session?.user?.id) {
      fetchConversations();
    }
  }, [session?.user?.id, fetchConversations]);

  return {
    conversations,
    messages,
    currentConversation,
    typingUsers,
    isLoading,
    sendMessage,
    createConversation,
    joinConversation,
    startTyping,
    stopTyping,
    fetchConversations,
  };
}
