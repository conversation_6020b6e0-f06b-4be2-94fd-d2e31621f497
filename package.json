{"name": "chat-app", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "build": "next build", "start": "NODE_ENV=production node server.js", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/multer": "^2.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "multer": "^2.0.2", "next": "15.4.2", "next-auth": "^4.24.11", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@jest/globals": "^30.0.4", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^3.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "prettier": "^3.6.2", "supertest": "^7.1.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}