import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prismaTest: PrismaClient | undefined
}

export const prismaTest =
  globalForPrisma.prismaTest ??
  new PrismaClient({
    datasources: {
      db: {
        url: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL,
      },
    },
  })

if (process.env.NODE_ENV !== 'production') globalForPrisma.prismaTest = prismaTest

// Test database utilities
export async function cleanupDatabase() {
  const tablenames = await prismaTest.$queryRaw<
    Array<{ tablename: string }>
  >`SELECT tablename FROM pg_tables WHERE schemaname='public'`

  const tables = tablenames
    .map(({ tablename }) => tablename)
    .filter((name) => name !== '_prisma_migrations')
    .map((name) => `"public"."${name}"`)
    .join(', ')

  try {
    await prismaTest.$executeRawUnsafe(`TRUNCATE TABLE ${tables} CASCADE;`)
  } catch (error) {
    console.log({ error })
  }
}

export async function createTestUser(data: {
  name: string
  email: string
  hashedPassword?: string
}) {
  return await prismaTest.user.create({
    data: {
      name: data.name,
      email: data.email,
      hashedPassword: data.hashedPassword || 'test-password-hash',
    },
  })
}

export async function createTestConversation(data: {
  isGroup?: boolean
  name?: string
  participantIds: string[]
}) {
  return await prismaTest.conversation.create({
    data: {
      isGroup: data.isGroup || false,
      name: data.name,
      participants: {
        create: data.participantIds.map((userId) => ({
          userId,
        })),
      },
    },
    include: {
      participants: {
        include: {
          user: true,
        },
      },
    },
  })
}

export async function createTestMessage(data: {
  body?: string
  image?: string
  senderId: string
  conversationId: string
}) {
  return await prismaTest.message.create({
    data: {
      body: data.body,
      image: data.image,
      senderId: data.senderId,
      conversationId: data.conversationId,
    },
    include: {
      sender: true,
      seenBy: {
        include: {
          user: true,
        },
      },
    },
  })
}
