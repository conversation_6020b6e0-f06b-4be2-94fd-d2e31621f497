'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Paperclip, X, Upload, File, Image } from 'lucide-react';

interface FileUploadProps {
  onFileSelect: (fileUrl: string, fileName: string, fileType: string) => void;
  disabled?: boolean;
}

export default function FileUpload({ onFileSelect, disabled = false }: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      alert('File size too large. Maximum size is 10MB.');
      return;
    }

    // Validate file type
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];

    if (!allowedTypes.includes(file.type)) {
      alert('File type not allowed. Please select an image, PDF, or document file.');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.ok) {
        const data = await response.json();
        onFileSelect(data.url, data.filename, data.type);
      } else {
        const errorData = await response.json();
        alert(errorData.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileChange}
        accept="image/*,.pdf,.txt,.doc,.docx"
        className="hidden"
      />
      
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleFileSelect}
        disabled={disabled || isUploading}
        className="relative"
      >
        {isUploading ? (
          <div className="flex items-center space-x-2">
            <Upload className="h-4 w-4 animate-pulse" />
            <span className="text-xs">{uploadProgress}%</span>
          </div>
        ) : (
          <Paperclip className="h-4 w-4" />
        )}
      </Button>
    </>
  );
}

interface FilePreviewProps {
  fileUrl: string;
  fileName: string;
  fileType: string;
  onRemove: () => void;
}

export function FilePreview({ fileUrl, fileName, fileType, onRemove }: FilePreviewProps) {
  const isImage = fileType.startsWith('image/');

  return (
    <div className="flex items-center space-x-2 p-2 bg-gray-100 rounded-lg">
      <div className="flex-shrink-0">
        {isImage ? (
          <div className="relative">
            <img
              src={fileUrl}
              alt={fileName}
              className="w-12 h-12 object-cover rounded"
            />
          </div>
        ) : (
          <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
            <File className="h-6 w-6 text-gray-500" />
          </div>
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">
          {fileName}
        </p>
        <p className="text-xs text-gray-500">
          {fileType}
        </p>
      </div>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={onRemove}
        className="flex-shrink-0"
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  );
}
