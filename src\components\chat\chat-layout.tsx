'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Sidebar from './sidebar';
import ChatArea from './chat-area';

export default function ChatLayout() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isMobile, setIsMobile] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push('/auth/signin');
    }
  }, [session, status, router]);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setShowSidebar(false);
      } else {
        setShowSidebar(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect to signin
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar - hidden on mobile when showSidebar is false */}
      <div className={`${isMobile ? (showSidebar ? 'fixed inset-0 z-50' : 'hidden') : 'relative'}`}>
        <Sidebar
          onClose={() => setShowSidebar(false)}
          isMobile={isMobile}
        />
      </div>

      {/* Chat Area */}
      <div className={`flex-1 ${isMobile && showSidebar ? 'hidden' : 'flex'}`}>
        <ChatArea
          onToggleSidebar={() => setShowSidebar(!showSidebar)}
          isMobile={isMobile}
        />
      </div>
    </div>
  );
}
