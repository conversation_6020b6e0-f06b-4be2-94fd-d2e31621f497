import { NextRequest } from 'next/server'
import { GET, POST } from '../route'
import { 
  cleanupDatabase, 
  prismaTest, 
  createTestUser, 
  createTestConversation, 
  createTestMessage 
} from '@/lib/test-db'

// Mock NextAuth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

import { getServerSession } from 'next-auth'
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>

describe('/api/conversations/[conversationId]/messages', () => {
  let testUser1: any
  let testUser2: any
  let testConversation: any

  beforeEach(async () => {
    await cleanupDatabase()
    
    testUser1 = await createTestUser({
      name: 'Test User 1',
      email: '<EMAIL>',
    })
    
    testUser2 = await createTestUser({
      name: 'Test User 2',
      email: '<EMAIL>',
    })

    testConversation = await createTestConversation({
      participantIds: [testUser1.id, testUser2.id],
    })

    mockGetServerSession.mockResolvedValue({
      user: { id: testUser1.id },
    } as any)
  })

  afterAll(async () => {
    await cleanupDatabase()
    await prismaTest.$disconnect()
  })

  describe('GET /api/conversations/[conversationId]/messages', () => {
    it('returns messages for a conversation', async () => {
      // Create test messages
      await createTestMessage({
        body: 'Hello!',
        senderId: testUser1.id,
        conversationId: testConversation.id,
      })

      await createTestMessage({
        body: 'Hi there!',
        senderId: testUser2.id,
        conversationId: testConversation.id,
      })

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`
      )
      
      const response = await GET(request, { 
        params: { conversationId: testConversation.id } 
      })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.messages).toHaveLength(2)
      expect(data.messages[0].body).toBe('Hello!')
      expect(data.messages[1].body).toBe('Hi there!')
    })

    it('returns empty array when conversation has no messages', async () => {
      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`
      )
      
      const response = await GET(request, { 
        params: { conversationId: testConversation.id } 
      })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.messages).toHaveLength(0)
    })

    it('returns 404 when conversation not found', async () => {
      const request = new NextRequest(
        'http://localhost:3000/api/conversations/nonexistent/messages'
      )
      
      const response = await GET(request, { 
        params: { conversationId: 'nonexistent' } 
      })
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Conversation not found')
    })

    it('returns 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`
      )
      
      const response = await GET(request, { 
        params: { conversationId: testConversation.id } 
      })
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })

  describe('POST /api/conversations/[conversationId]/messages', () => {
    it('creates a new message', async () => {
      const requestBody = {
        body: 'Test message',
      }

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`,
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const response = await POST(request, { 
        params: { conversationId: testConversation.id } 
      })
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.message.body).toBe('Test message')
      expect(data.message.senderId).toBe(testUser1.id)
      expect(data.message.conversationId).toBe(testConversation.id)
    })

    it('creates a message with image', async () => {
      const requestBody = {
        body: 'Check this out!',
        image: '/uploads/test-image.jpg',
      }

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`,
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const response = await POST(request, { 
        params: { conversationId: testConversation.id } 
      })
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.message.body).toBe('Check this out!')
      expect(data.message.image).toBe('/uploads/test-image.jpg')
    })

    it('returns error when both body and image are missing', async () => {
      const requestBody = {}

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`,
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const response = await POST(request, { 
        params: { conversationId: testConversation.id } 
      })
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Message body or image is required')
    })

    it('returns 404 when conversation not found', async () => {
      const requestBody = {
        body: 'Test message',
      }

      const request = new NextRequest(
        'http://localhost:3000/api/conversations/nonexistent/messages',
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const response = await POST(request, { 
        params: { conversationId: 'nonexistent' } 
      })
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Conversation not found')
    })

    it('returns 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const requestBody = {
        body: 'Test message',
      }

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`,
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const response = await POST(request, { 
        params: { conversationId: testConversation.id } 
      })
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })
})
