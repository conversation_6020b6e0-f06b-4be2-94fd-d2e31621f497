const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const { Server } = require('socket.io');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3000;

const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  const server = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  const io = new Server(server, {
    cors: {
      origin: process.env.NEXTAUTH_URL || `http://${hostname}:${port}`,
      methods: ['GET', 'POST'],
    },
  });

  io.on('connection', (socket) => {
    console.log('User connected:', socket.id);

    // Join user to their personal room
    socket.on('join-user', (userId) => {
      socket.join(`user:${userId}`);
      socket.data.userId = userId;
      console.log(`User ${userId} joined their room`);
    });

    // Join conversation room
    socket.on('join-conversation', (conversationId) => {
      socket.join(`conversation:${conversationId}`);
      console.log(`User joined conversation: ${conversationId}`);
    });

    // Leave conversation room
    socket.on('leave-conversation', (conversationId) => {
      socket.leave(`conversation:${conversationId}`);
      console.log(`User left conversation: ${conversationId}`);
    });

    // Handle new message
    socket.on('send-message', (data) => {
      console.log('New message:', data);
      // Broadcast to conversation room
      socket.to(`conversation:${data.conversationId}`).emit('new-message', data);
    });

    // Handle typing indicators
    socket.on('typing-start', (data) => {
      socket.to(`conversation:${data.conversationId}`).emit('user-typing', {
        userId: data.userId,
        userName: data.userName,
      });
    });

    socket.on('typing-stop', (data) => {
      socket.to(`conversation:${data.conversationId}`).emit('user-stop-typing', {
        userId: data.userId,
      });
    });

    // Handle user presence
    socket.on('user-online', (userId) => {
      socket.broadcast.emit('user-status-change', {
        userId,
        status: 'online',
      });
    });

    socket.on('disconnect', () => {
      console.log('User disconnected:', socket.id);
      // Handle user going offline
      if (socket.data?.userId) {
        socket.broadcast.emit('user-status-change', {
          userId: socket.data.userId,
          status: 'offline',
        });
      }
    });
  });

  server
    .once('error', (err) => {
      console.error(err);
      process.exit(1);
    })
    .listen(port, () => {
      console.log(`> Ready on http://${hostname}:${port}`);
    });
});
