'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Send, Paperclip, Smile, Menu } from 'lucide-react';

interface ChatAreaProps {
  onToggleSidebar?: () => void;
  isMobile?: boolean;
}

export default function ChatArea({ onToggleSidebar, isMobile = false }: ChatAreaProps) {
  const [message, setMessage] = useState('');

  // Mock messages data - will be replaced with real data later
  const messages = [
    {
      id: '1',
      content: 'Hey there! How are you doing?',
      senderId: 'other',
      senderName: '<PERSON>',
      timestamp: '10:30 AM',
      isOwn: false,
    },
    {
      id: '2',
      content: 'I\'m doing great! Thanks for asking. How about you?',
      senderId: 'me',
      senderName: 'Me',
      timestamp: '10:32 AM',
      isOwn: true,
    },
    {
      id: '3',
      content: 'Pretty good! Working on some exciting projects.',
      senderId: 'other',
      senderName: '<PERSON>e',
      timestamp: '10:35 AM',
      isOwn: false,
    },
    {
      id: '4',
      content: 'That sounds awesome! Would love to hear more about it.',
      senderId: 'me',
      senderName: 'Me',
      timestamp: '10:36 AM',
      isOwn: true,
    },
  ];

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      // TODO: Send message logic
      console.log('Sending message:', message);
      setMessage('');
    }
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* Chat Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center space-x-3">
          {isMobile && (
            <Button variant="ghost" size="sm" onClick={onToggleSidebar}>
              <Menu className="h-4 w-4" />
            </Button>
          )}
          <Avatar>
            <AvatarFallback>JD</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="font-semibold text-gray-900">John Doe</h2>
            <p className="text-sm text-gray-500">Online</p>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((msg) => (
            <div
              key={msg.id}
              className={`flex ${msg.isOwn ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  msg.isOwn
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-900'
                }`}
              >
                <p className="text-sm">{msg.content}</p>
                <p
                  className={`text-xs mt-1 ${
                    msg.isOwn ? 'text-blue-100' : 'text-gray-500'
                  }`}
                >
                  {msg.timestamp}
                </p>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Message Input */}
      <div className="bg-white border-t border-gray-200 p-4">
        <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
          <Button type="button" variant="ghost" size="sm">
            <Paperclip className="h-4 w-4" />
          </Button>
          <div className="flex-1 relative">
            <Input
              placeholder="Type a message..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>
          <Button type="submit" disabled={!message.trim()}>
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </div>
  );
}
