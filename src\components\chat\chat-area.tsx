'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Send, Paperclip, Smile, Menu } from 'lucide-react';
import { useChat } from '@/hooks/use-chat';
import { useSession } from 'next-auth/react';
import { useMessageStatus } from '@/hooks/use-message-status';
import { useSocket } from '@/components/providers/socket-provider';
import FileUpload, { FilePreview } from './file-upload';

interface ChatAreaProps {
  onToggleSidebar?: () => void;
  isMobile?: boolean;
}

export default function ChatArea({ onToggleSidebar, isMobile = false }: ChatAreaProps) {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [selectedFile, setSelectedFile] = useState<{
    url: string;
    name: string;
    type: string;
  } | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const { data: session } = useSession();
  const { socket } = useSocket();
  const {
    messages,
    currentConversation,
    conversations,
    typingUsers,
    sendMessage,
    startTyping,
    stopTyping,
    setMessages,
  } = useChat();

  const {
    markMessageAsSeen,
    markMessagesAsSeen,
    getMessageStatus,
  } = useMessageStatus();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Mark messages as seen when conversation changes or new messages arrive
  useEffect(() => {
    if (!currentConversation || !session?.user?.id || messages.length === 0) return;

    const unseenMessages = messages.filter(msg =>
      msg.senderId !== session.user.id &&
      !msg.seenBy?.some(seen => seen.user.id === session.user.id)
    );

    if (unseenMessages.length > 0) {
      const messageIds = unseenMessages.map(msg => msg.id);
      markMessagesAsSeen(currentConversation, messageIds);
    }
  }, [currentConversation, messages, session?.user?.id, markMessagesAsSeen]);

  // Handle typing indicators
  const handleTyping = () => {
    if (!currentConversation || !session?.user?.id) return;

    if (!isTyping) {
      setIsTyping(true);
      startTyping(currentConversation);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      stopTyping(currentConversation);
    }, 1000);
  };

  // Clean up typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  // Get typing users (excluding current user)
  const typingUsersList = Array.from(typingUsers).filter(
    userId => userId !== session?.user?.id
  );

  // Get current conversation details
  const currentConversationData = conversations.find(
    conv => conv.id === currentConversation
  );

  // Get conversation display name and info
  const getConversationInfo = () => {
    if (!currentConversationData) return { name: 'Conversation', subtitle: 'Online' };

    if (currentConversationData.isGroup) {
      const participantCount = currentConversationData.participants?.length || 0;
      return {
        name: currentConversationData.name || 'Group Chat',
        subtitle: `${participantCount} members`,
      };
    }

    // For one-on-one chats, find the other participant
    const otherParticipant = currentConversationData.participants?.find(
      (p: any) => p.user.id !== session?.user?.id
    );

    if (otherParticipant) {
      return {
        name: otherParticipant.user.name || 'Unknown User',
        subtitle: typingUsersList.length > 0 ? 'typing...' : 'Online',
      };
    }

    return { name: 'Conversation', subtitle: 'Online' };
  };

  const conversationInfo = getConversationInfo();

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if ((message.trim() || selectedFile) && currentConversation) {
      // Stop typing indicator
      if (isTyping) {
        setIsTyping(false);
        stopTyping(currentConversation);
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
      }

      // Send message with file if selected
      if (selectedFile) {
        await sendMessageWithFile(currentConversation, message, selectedFile.url);
        setSelectedFile(null);
      } else {
        await sendMessage(currentConversation, message);
      }

      setMessage('');
    }
  };

  const sendMessageWithFile = async (conversationId: string, body: string, imageUrl: string) => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ body: body || '', image: imageUrl }),
      });

      const data = await response.json();

      if (response.ok && socket) {
        // Emit to Socket.IO for real-time updates
        socket.emit('send-message', {
          ...data.message,
          conversationId,
        });

        // Add message to local state
        setMessages(prev => [...prev, data.message]);
      }
    } catch (error) {
      console.error('Error sending message with file:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(e.target.value);
    handleTyping();
  };

  const handleFileSelect = (fileUrl: string, fileName: string, fileType: string) => {
    setSelectedFile({
      url: fileUrl,
      name: fileName,
      type: fileType,
    });
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
  };

  if (!currentConversation) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Welcome to Chat App
          </h3>
          <p className="text-gray-500">
            Select a conversation to start messaging
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Chat Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center space-x-3">
          {isMobile && (
            <Button variant="ghost" size="sm" onClick={onToggleSidebar}>
              <Menu className="h-4 w-4" />
            </Button>
          )}
          <Avatar>
            <AvatarFallback>
              {currentConversationData?.isGroup ? 'G' : conversationInfo.name.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div>
            <h2 className="font-semibold text-gray-900">
              {conversationInfo.name}
            </h2>
            <p className="text-sm text-gray-500">
              {conversationInfo.subtitle}
            </p>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((msg) => {
            const isOwn = msg.senderId === session?.user?.id;
            return (
              <div
                key={msg.id}
                className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    isOwn
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-900'
                  }`}
                >
                  {!isOwn && (
                    <p className="text-xs font-medium mb-1 opacity-70">
                      {msg.sender.name}
                    </p>
                  )}

                  {/* Display image if present */}
                  {msg.image && (
                    <div className="mb-2">
                      <img
                        src={msg.image}
                        alt="Shared image"
                        className="max-w-xs rounded-lg cursor-pointer"
                        onClick={() => window.open(msg.image, '_blank')}
                      />
                    </div>
                  )}

                  {/* Display text message if present */}
                  {msg.body && <p className="text-sm">{msg.body}</p>}
                  <div className="flex items-center justify-between mt-1">
                    <p
                      className={`text-xs ${
                        isOwn ? 'text-blue-100' : 'text-gray-500'
                      }`}
                    >
                      {new Date(msg.createdAt).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </p>
                    {isOwn && (
                      <p
                        className={`text-xs ${
                          isOwn ? 'text-blue-100' : 'text-gray-500'
                        }`}
                      >
                        {getMessageStatus(msg, session?.user?.id || '')}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            );
          })}

          {/* Typing indicator */}
          {typingUsersList.length > 0 && (
            <div className="flex justify-start">
              <div className="bg-gray-200 text-gray-900 px-4 py-2 rounded-lg max-w-xs">
                <div className="flex items-center space-x-1">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                  <span className="text-xs text-gray-500 ml-2">typing...</span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Message Input */}
      <div className="bg-white border-t border-gray-200 p-4">
        <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
          <Button type="button" variant="ghost" size="sm">
            <Paperclip className="h-4 w-4" />
          </Button>
          <div className="flex-1 relative">
            <Input
              placeholder="Type a message..."
              value={message}
              onChange={handleInputChange}
              className="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>
          <Button type="submit" disabled={!message.trim()}>
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </div>
  );
}
