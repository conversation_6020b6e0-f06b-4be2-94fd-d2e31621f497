import { NextRequest } from 'next/server'
import { POST } from '../register/route'
import { cleanupDatabase, prismaTest } from '@/lib/test-db'

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashed-password'),
}))

describe('/api/auth/register', () => {
  beforeEach(async () => {
    await cleanupDatabase()
  })

  afterAll(async () => {
    await cleanupDatabase()
    await prismaTest.$disconnect()
  })

  it('creates a new user successfully', async () => {
    const requestBody = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    }

    const request = new NextRequest('http://localhost:3000/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(201)
    expect(data.message).toBe('User created successfully')
    expect(data.user).toMatchObject({
      name: 'Test User',
      email: '<EMAIL>',
    })
    expect(data.user.hashedPassword).toBeUndefined() // Should not return password
  })

  it('returns error for missing fields', async () => {
    const requestBody = {
      name: 'Test User',
      // Missing email and password
    }

    const request = new NextRequest('http://localhost:3000/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Missing required fields')
  })

  it('returns error for existing user', async () => {
    // Create a user first
    await prismaTest.user.create({
      data: {
        name: 'Existing User',
        email: '<EMAIL>',
        hashedPassword: 'hashed-password',
      },
    })

    const requestBody = {
      name: 'Test User',
      email: '<EMAIL>', // Same email
      password: 'password123',
    }

    const request = new NextRequest('http://localhost:3000/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('User already exists')
  })

  it('handles database errors gracefully', async () => {
    // Mock Prisma to throw an error
    const originalCreate = prismaTest.user.create
    prismaTest.user.create = jest.fn().mockRejectedValue(new Error('Database error'))

    const requestBody = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    }

    const request = new NextRequest('http://localhost:3000/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data.error).toBe('Internal server error')

    // Restore original method
    prismaTest.user.create = originalCreate
  })
})
