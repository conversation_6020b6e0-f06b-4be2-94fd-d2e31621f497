import { NextRequest, NextResponse } from 'next/server';
import { Server as ServerIO } from 'socket.io';
import { Server as NetServer } from 'http';

let io: ServerIO;

export async function GET(req: NextRequest) {
  if (!io) {
    console.log('Initializing Socket.IO server...');
    
    // Create HTTP server
    const httpServer: NetServer = (req as any).socket?.server;
    
    if (!httpServer) {
      return NextResponse.json({ error: 'HTTP server not available' }, { status: 500 });
    }

    io = new ServerIO(httpServer, {
      path: '/api/socket',
      addTrailingSlash: false,
      cors: {
        origin: process.env.NEXTAUTH_URL || 'http://localhost:3000',
        methods: ['GET', 'POST'],
      },
    });

    io.on('connection', (socket) => {
      console.log('User connected:', socket.id);

      // Join user to their personal room
      socket.on('join-user', (userId: string) => {
        socket.join(`user:${userId}`);
        console.log(`User ${userId} joined their room`);
      });

      // Join conversation room
      socket.on('join-conversation', (conversationId: string) => {
        socket.join(`conversation:${conversationId}`);
        console.log(`User joined conversation: ${conversationId}`);
      });

      // Leave conversation room
      socket.on('leave-conversation', (conversationId: string) => {
        socket.leave(`conversation:${conversationId}`);
        console.log(`User left conversation: ${conversationId}`);
      });

      // Handle new message
      socket.on('send-message', (data) => {
        console.log('New message:', data);
        // Broadcast to conversation room
        socket.to(`conversation:${data.conversationId}`).emit('new-message', data);
      });

      // Handle typing indicators
      socket.on('typing-start', (data) => {
        socket.to(`conversation:${data.conversationId}`).emit('user-typing', {
          userId: data.userId,
          userName: data.userName,
        });
      });

      socket.on('typing-stop', (data) => {
        socket.to(`conversation:${data.conversationId}`).emit('user-stop-typing', {
          userId: data.userId,
        });
      });

      // Handle user presence
      socket.on('user-online', (userId: string) => {
        socket.broadcast.emit('user-status-change', {
          userId,
          status: 'online',
        });
      });

      socket.on('disconnect', () => {
        console.log('User disconnected:', socket.id);
        // Handle user going offline
        socket.broadcast.emit('user-status-change', {
          userId: socket.data?.userId,
          status: 'offline',
        });
      });
    });

    console.log('Socket.IO server initialized');
  }

  return NextResponse.json({ message: 'Socket.IO server running' });
}
