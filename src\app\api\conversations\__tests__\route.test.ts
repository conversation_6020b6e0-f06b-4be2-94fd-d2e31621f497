import { NextRequest } from 'next/server'
import { GET, POST } from '../route'
import { cleanupDatabase, prismaTest, createTestUser, createTestConversation } from '@/lib/test-db'

// Mock NextAuth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

import { getServerSession } from 'next-auth'
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>

describe('/api/conversations', () => {
  let testUser1: any
  let testUser2: any

  beforeEach(async () => {
    await cleanupDatabase()
    
    testUser1 = await createTestUser({
      name: 'Test User 1',
      email: '<EMAIL>',
    })
    
    testUser2 = await createTestUser({
      name: 'Test User 2',
      email: '<EMAIL>',
    })

    mockGetServerSession.mockResolvedValue({
      user: { id: testUser1.id },
    } as any)
  })

  afterAll(async () => {
    await cleanupDatabase()
    await prismaTest.$disconnect()
  })

  describe('GET /api/conversations', () => {
    it('returns user conversations', async () => {
      // Create a conversation
      await createTestConversation({
        participantIds: [testUser1.id, testUser2.id],
      })

      const request = new NextRequest('http://localhost:3000/api/conversations')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.conversations).toHaveLength(1)
      expect(data.conversations[0].participants).toHaveLength(2)
    })

    it('returns empty array when user has no conversations', async () => {
      const request = new NextRequest('http://localhost:3000/api/conversations')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.conversations).toHaveLength(0)
    })

    it('returns 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/conversations')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })

  describe('POST /api/conversations', () => {
    it('creates a new one-on-one conversation', async () => {
      const requestBody = {
        participantIds: [testUser2.id],
        isGroup: false,
      }

      const request = new NextRequest('http://localhost:3000/api/conversations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.conversation.isGroup).toBe(false)
      expect(data.conversation.participants).toHaveLength(2)
    })

    it('creates a new group conversation', async () => {
      const testUser3 = await createTestUser({
        name: 'Test User 3',
        email: '<EMAIL>',
      })

      const requestBody = {
        participantIds: [testUser2.id, testUser3.id],
        isGroup: true,
        name: 'Test Group',
      }

      const request = new NextRequest('http://localhost:3000/api/conversations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.conversation.isGroup).toBe(true)
      expect(data.conversation.name).toBe('Test Group')
      expect(data.conversation.participants).toHaveLength(3) // Including current user
    })

    it('returns existing conversation for one-on-one chat', async () => {
      // Create existing conversation
      const existingConversation = await createTestConversation({
        participantIds: [testUser1.id, testUser2.id],
      })

      const requestBody = {
        participantIds: [testUser2.id],
        isGroup: false,
      }

      const request = new NextRequest('http://localhost:3000/api/conversations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.conversation.id).toBe(existingConversation.id)
    })

    it('returns error for missing participant IDs', async () => {
      const requestBody = {
        isGroup: false,
      }

      const request = new NextRequest('http://localhost:3000/api/conversations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Participant IDs are required')
    })

    it('returns 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const requestBody = {
        participantIds: [testUser2.id],
        isGroup: false,
      }

      const request = new NextRequest('http://localhost:3000/api/conversations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })
})
