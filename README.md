# 💬 Real-Time Chat Application

A modern, full-stack real-time chat application built with Next.js, featuring instant messaging, group chats, file sharing, and comprehensive user management.

![Chat App Screenshot](https://via.placeholder.com/800x400/4F46E5/FFFFFF?text=Chat+App+Screenshot)

## ✨ Features

### 🔐 Authentication & User Management
- **Secure Registration & Login** - Email/password authentication with bcrypt hashing
- **OAuth Integration** - Google OAuth for seamless social login
- **User Profiles** - Customizable profiles with display names and profile pictures
- **Session Management** - Secure session handling with NextAuth.js

### 💬 Real-Time Messaging
- **Instant Messaging** - Real-time message delivery using Socket.IO
- **One-on-One Chats** - Private conversations between users
- **Group Conversations** - Create and manage group chats with multiple participants
- **Typing Indicators** - See when someone is typing a message
- **User Presence** - Online/offline status indicators
- **Message Status** - Sent, delivered, and seen indicators

### 📁 File Sharing
- **Image Sharing** - Upload and share images in conversations
- **File Support** - Support for documents (PDF, DOC, TXT)
- **File Preview** - Inline image previews and file information
- **Size Limits** - Configurable file size restrictions (default: 10MB)

### 📱 Modern UI/UX
- **Responsive Design** - Works seamlessly on desktop and mobile devices
- **Dark/Light Theme** - Built with Tailwind CSS and Shadcn/ui components
- **Intuitive Interface** - Clean, modern design with smooth animations
- **Search Functionality** - Find users and conversations quickly

### 🔍 Advanced Features
- **Message History** - Persistent message storage and retrieval
- **User Search** - Find and connect with other users
- **Conversation Management** - Create, join, and manage conversations
- **Real-time Updates** - Instant updates across all connected clients

## 🛠️ Technology Stack

### Frontend
- **[Next.js 14](https://nextjs.org/)** - React framework with App Router
- **[TypeScript](https://www.typescriptlang.org/)** - Type-safe JavaScript
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **[Shadcn/ui](https://ui.shadcn.com/)** - Beautiful, accessible UI components
- **[React Hook Form](https://react-hook-form.com/)** - Performant forms with validation

### Backend
- **[Next.js API Routes](https://nextjs.org/docs/api-routes/introduction)** - Serverless API endpoints
- **[NextAuth.js](https://next-auth.js.org/)** - Authentication for Next.js
- **[Prisma](https://www.prisma.io/)** - Type-safe database ORM
- **[PostgreSQL](https://www.postgresql.org/)** - Robust relational database

### Real-Time Communication
- **[Socket.IO](https://socket.io/)** - Real-time bidirectional event-based communication
- **Custom Socket Server** - Integrated with Next.js for seamless real-time features

### DevOps & Deployment
- **[Docker](https://www.docker.com/)** - Containerization for consistent deployments
- **[Docker Compose](https://docs.docker.com/compose/)** - Multi-container orchestration
- **Environment Configuration** - Flexible environment-based configuration

### Testing
- **[Jest](https://jestjs.io/)** - JavaScript testing framework
- **[React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)** - Simple and complete testing utilities
- **Integration Tests** - Comprehensive API and database testing

## 🚀 Quick Start

### Prerequisites
- **Node.js** 18+
- **npm** or **yarn**
- **PostgreSQL** (or use Docker)
- **Docker & Docker Compose** (for containerized setup)

### Option 1: Docker Setup (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chat-app
   ```

2. **Set up environment variables**
   ```bash
   cp .env.docker .env
   # Edit .env with your configuration
   ```

3. **Start with Docker Compose**
   ```bash
   # Using the setup script (Linux/Mac)
   chmod +x scripts/setup-docker.sh
   ./scripts/setup-docker.sh

   # Or manually
   docker-compose up --build
   ```

4. **Access the application**
   - **Application**: http://localhost:3000
   - **Database Admin**: http://localhost:8080 (if using dev setup)

### Option 2: Local Development Setup

1. **Clone and install dependencies**
   ```bash
   git clone <repository-url>
   cd chat-app
   npm install
   ```

2. **Set up the database**
   ```bash
   # Start PostgreSQL (or use Docker)
   docker-compose -f docker-compose.dev.yml up -d
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your database URL and other settings
   ```

4. **Set up the database schema**
   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   ```
   http://localhost:3000
   ```

## 📋 Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/chat_app"

# NextAuth.js
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# File Upload
MAX_FILE_SIZE="10485760"  # 10MB
```

### Required Variables
- `DATABASE_URL` - PostgreSQL connection string
- `NEXTAUTH_SECRET` - Secret key for NextAuth.js (generate with `openssl rand -base64 32`)
- `NEXTAUTH_URL` - Your application URL

### Optional Variables
- `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET` - For Google OAuth
- `MAX_FILE_SIZE` - Maximum file upload size in bytes

## 🧪 Testing

### Run All Tests
```bash
npm test
```

### Run Tests in Watch Mode
```bash
npm run test:watch
```

### Run Tests with Coverage
```bash
npm run test:coverage
```

### Test Categories
- **Unit Tests** - Component and utility function tests
- **Integration Tests** - API route and database tests
- **Coverage Reports** - Detailed coverage analysis

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout

### User Endpoints
- `GET /api/users/search` - Search users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `POST /api/users/presence` - Update user presence

### Conversation Endpoints
- `GET /api/conversations` - Get user conversations
- `POST /api/conversations` - Create new conversation
- `GET /api/conversations/[id]/messages` - Get conversation messages
- `POST /api/conversations/[id]/messages` - Send message

### File Upload
- `POST /api/upload` - Upload files

## 🐳 Docker Commands

### Development
```bash
# Start development database only
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production
```bash
# Build and start all services
docker-compose up --build -d

# Scale the application
docker-compose up --scale app=3

# Update and restart
docker-compose pull && docker-compose up -d
```

## 🔧 Development Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run db:generate     # Generate Prisma client
npm run db:push         # Push schema to database
npm run db:migrate      # Run database migrations
npm run db:studio       # Open Prisma Studio

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint issues
npm run format          # Format code with Prettier
npm run type-check      # TypeScript type checking

# Testing
npm run test            # Run tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage
```

## 🏗️ Project Structure

```
chat-app/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── api/            # API routes
│   │   ├── auth/           # Authentication pages
│   │   └── profile/        # User profile page
│   ├── components/         # React components
│   │   ├── ui/            # Shadcn/ui components
│   │   ├── chat/          # Chat-specific components
│   │   └── providers/     # Context providers
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility libraries
│   └── types/             # TypeScript type definitions
├── prisma/                # Database schema and migrations
├── scripts/               # Setup and utility scripts
├── public/                # Static assets
├── docker-compose.yml     # Production Docker setup
├── docker-compose.dev.yml # Development Docker setup
├── Dockerfile            # Application container
└── README.md             # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [Prisma](https://www.prisma.io/) for the excellent database toolkit
- [Socket.IO](https://socket.io/) for real-time communication
- [Shadcn/ui](https://ui.shadcn.com/) for beautiful UI components
- [Tailwind CSS](https://tailwindcss.com/) for utility-first styling

---

**Built with ❤️ using modern web technologies**
